light:
  - platform: esp32_rmt_led_strip
    id: led_ring
    name: 'LED Ring'
    pin: GPIO38
    num_leds: 8
    chipset: WS2812
    rgb_order: GRB
    rmt_symbols: 96
    effects:
      - pulse:
      - random:
      - strobe:
      - flicker:
      - addressable_rainbow:
      - addressable_color_wipe:
      - addressable_scan:
      - addressable_twinkle:
      - addressable_random_twinkle:
      - addressable_fireworks:
      - addressable_flicker:
      - addressable_lambda:
          name: 'Waiting for Command'
          update_interval: 100ms
          lambda: |-
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255,
                  light_color.get_blue() * 255);
            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 11) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 10) % 12) {
                it[i] = color * 128;
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 5) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 4) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }
            id(global_led_animation_index) = (id(global_led_animation_index) + 1) % 12;
      - addressable_lambda:
          name: 'Listening For Command'
          update_interval: 50ms
          lambda: |-
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255,
                  light_color.get_blue() * 255);
            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 11) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 10) % 12) {
                it[i] = color * 128;
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == (id(global_led_animation_index) + 5) % 12) {
                it[i] = color * 192;
              } else if (i == (id(global_led_animation_index) + 4) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }
            id(global_led_animation_index) = (id(global_led_animation_index) + 1) % 12;
      - addressable_lambda:
          name: 'Thinking'
          update_interval: 10ms
          lambda: |-
            static uint8_t brightness_step = 0;
            static bool brightness_decreasing = true;
            static uint8_t brightness_step_number = 10;
            if (initial_run) {
              brightness_step = 0;
              brightness_decreasing = true;
            }
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255,
                  light_color.get_blue() * 255);
            for (uint8_t i = 0; i < 12; i++) {
              if (i == id(global_led_animation_index) % 12) {
                it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
              } else if (i == (id(global_led_animation_index) + 6) % 12) {
                it[i] = color * uint8_t(255/brightness_step_number*(brightness_step_number-brightness_step));
              } else {
                it[i] = Color::BLACK;
              }
            }
            if (brightness_decreasing) {
              brightness_step++;
            } else {
              brightness_step--;
            }
            if (brightness_step == 0 || brightness_step == brightness_step_number) {
              brightness_decreasing = !brightness_decreasing;
            }
      - addressable_lambda:
          name: 'Replying'
          update_interval: 50ms
          lambda: |-
            id(global_led_animation_index) = (12 + id(global_led_animation_index) - 1) % 12;
            auto light_color = id(led_ring).current_values;
            Color color(light_color.get_red() * 255, light_color.get_green() * 255,
                  light_color.get_blue() * 255);
            for (uint8_t i = 0; i < 12; i++) {
              if (i == (id(global_led_animation_index)) % 12) {
                it[i] = color;
              } else if (i == ( id(global_led_animation_index) + 1) % 12) {
                it[i] = color * 192;
              } else if (i == ( id(global_led_animation_index) + 2) % 12) {
                it[i] = color * 128;
              } else if (i == ( id(global_led_animation_index) + 6) % 12) {
                it[i] = color;
              } else if (i == ( id(global_led_animation_index) + 7) % 12) {
                it[i] = color * 192;
              } else if (i == ( id(global_led_animation_index) + 8) % 12) {
                it[i] = color * 128;
              } else {
                it[i] = Color::BLACK;
              }
            }
